import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import Navbar from '../../components/Navbar';
import DateTimePicker from '@react-native-community/datetimepicker';
import { fetchBusinessDay } from '../../apiHandling/businessDayAPI';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import { fetchBillReports, fetchBranchDetails } from '../../apiHandling/ReportAPI/fetchBillReportsAPI';
import { fetchItemReports } from '../../apiHandling/ReportAPI/fetchItemReportsAPI';
import { fetchBillDetails } from '../../apiHandling/BillingAPI/fetchBillDetailsAPI';

const DayReportScreen = () => {
  const navigation = useNavigation();

  // UI control states
  const [selectedScrollOption, setSelectedScrollOption] = useState('');

  // Report form states
  const [reportType, setReportType] = useState('');
  const [posTerminalNumber, setPosTerminalNumber] = useState(0);
  const [fromDate, setFromDate] = useState(null);
  const [toDate, setToDate] = useState(null);
  const [showFromDatePicker, setShowFromDatePicker] = useState(false);
  const [showToDatePicker, setShowToDatePicker] = useState(false);
  const [selectedBusinessChannel, setSelectedBusinessChannel] = useState('VAN');

  // Initialize dates on component mount
  useEffect(() => {
    const initializeDates = async () => {
      try {
        const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;

        const businessDate = await fetchBusinessDay(bearerToken, loginBranchID); // format: DD-MM-YYYY
        const [day, month, year] = businessDate.split('-');
        const from = new Date(`${year}-${month}-${day}`);
        const to = new Date(from);
        to.setDate(to.getDate() + 1); // next day

        setFromDate(from);
        setToDate(to);
      } catch (error) {
        console.error('Error initializing dates:', error);
        // Fallback to current date
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        setFromDate(today);
        setToDate(tomorrow);
      }
    };

    initializeDates();
  }, []);

  // Handle POS terminal number changes
  const handlePosTerminalIncrease = () => {
    setPosTerminalNumber(prev => prev + 1);
  };

  const handlePosTerminalDecrease = () => {
    setPosTerminalNumber(prev => prev > 0 ? prev - 1 : 0);
  };

  // Handle bill select for navigation to ViewBillScreen
  const handleBillSelect = async (saleId) => {
    try {
      const bearerToken = await AsyncStorage.getItem('authToken');
      const selectedBranch = await AsyncStorage.getItem('selectedBranch');
      const parsedBranch = JSON.parse(selectedBranch);
      const loginBranchID = parsedBranch.BranchId;

      const res = await fetchBillDetails(loginBranchID, saleId, bearerToken);

      if (res?.saleHeader?.length && res?.customerInfo?.length) {
        const saleHeader = res.saleHeader[0];
        const saleDetails = res.saleDetails || [];
        const customerInfo = res.customerInfo[0];

        const deliveryAddress =
          Array.isArray(res.deliveryAddress) && res.deliveryAddress.length > 0
            ? res.deliveryAddress[0]
            : {};

        const billingDetails =
          Array.isArray(res.billingDetails) && res.billingDetails.length > 0
            ? res.billingDetails[0]
            : {};

        // Fetch Branch Details using BranchId from saleHeader
        const branchIdFromSale = saleHeader.BranchId;
        const branchData = await fetchBranchDetails(branchIdFromSale, bearerToken);

        const billData = {
          saleID: saleHeader.SaleId ?? '',
          currentTimeStamp: saleHeader.BusinessDate ?? '',
          itemTableDetails: saleDetails,

          customerID: customerInfo.CustID ?? '',
          customerName: customerInfo.CustomerName ?? '',
          customerPhoneNumber: customerInfo.Mobile ?? '',

          customerAddress: deliveryAddress.CustomerAddress ?? '',
          customerPlace: deliveryAddress.PlaceName ?? '',
          customerCity: deliveryAddress.CityName ?? '',
          customerState: deliveryAddress.StateName ?? '',
          customerPincode: deliveryAddress.PINCODE ?? '',

          deliveryCharges: saleHeader.DeliveryCharge ?? 0,
          selectedGatewayName: billingDetails.PaymentGatewayName ?? '',
          selectedCustomerType: saleHeader.CustomerType ?? '',

          taxAmount: saleHeader.TaxAmount?.toString() ?? '0',
          discountAmount: saleHeader.DiscountAmount?.toString() ?? '0',
          totalAmount: saleHeader.TotalAmount?.toString() ?? '0',
          roundOffAmount: saleHeader.RoundOffAmount?.toString() ?? '0',

          printDateTime: new Date().toLocaleString(),

          // Branch Details from separate API
          branchId: branchData.BranchId ?? '',
          branchName: branchData.BranchName ?? '',
          areaName: branchData.AreaName ?? '',
          gstNumber: branchData.GSTNumber ?? '',
          foodLicenseNumber: branchData.FoodLicenseNumber ?? '',
          branchPincode: branchData.PINCODE ?? '',

          // Flag to indicate this is a duplicate bill view
          isDuplicate: true,
        };

        navigation.navigate('ViewBillScreen', billData);
      } else {
        Alert.alert('Error', 'Failed to load bill data.');
      }
    } catch (error) {
      console.error('Error in handleBillSelect:', error);
      Alert.alert('Error', 'Something went wrong while fetching bill details.');
    }
  };

  // Handle generate report
  const handleGenerateReport = async () => {
    if (!reportType) {
      Alert.alert('Error', 'Please select a report type');
      return;
    }
    if (!fromDate || !toDate) {
      Alert.alert('Error', 'Please select both from and to dates');
      return;
    }

    try {
      const bearerToken = await AsyncStorage.getItem('authToken');
      const selectedBranch = await AsyncStorage.getItem('selectedBranch');
      const parsedBranch = JSON.parse(selectedBranch);
      const loginBranchID = parsedBranch.BranchId;

      const fromStr = fromDate.toISOString().split('T')[0].replace(/-/g, '');
      const toStr = toDate.toISOString().split('T')[0].replace(/-/g, '');

      if (reportType === 'sale-bill-wise') {
        // Show loading dialog
        //Alert.alert('Loading', 'Fetching bill reports...');

        const reports = await fetchBillReports(
          loginBranchID,
          posTerminalNumber,
          fromStr,
          toStr,
          bearerToken
        );

        if (reports.length === 0) {
          Alert.alert('No Bills Found', 'No bills for the selected date range.');
        } else {
          // Navigate to bill report page (to be created)
          navigation.navigate('BillReportPage', {
            posTerminal: posTerminalNumber,
            billData: reports,
            fromDate,
            toDate,
          });
        }
      }

      if (reportType === 'sale-item-wise') {
        if (!selectedBusinessChannel) {
          Alert.alert('Error', 'Please select a business channel');
          return;
        }

        // Show loading dialog
        //Alert.alert('Loading', 'Fetching item reports...');

        const itemReports = await fetchItemReports(
          loginBranchID,
          posTerminalNumber,
          fromStr,
          toStr,
          selectedBusinessChannel,
          bearerToken
        );

        if (itemReports.length === 0) {
          Alert.alert('No Items Found', 'No items for the selected date range.');
        } else {
          // Navigate to item report page (to be created)
          navigation.navigate('ItemReportPage', {
            itemData: itemReports,
            fromDate,
            toDate,
          });
        }
      }
    } catch (error) {
      console.error('Error generating report:', error);
      Alert.alert('Error', `Something went wrong: ${error.message}`);
    }
  };

  return (
    <View style={styles.container}>
      <Navbar />

      {/* Scroll Options */}
      <View style={styles.scrollOptions_container}>
        <View style={styles.scrollOptions_row}>
          {/* Page Title */}
          <TouchableOpacity style={styles.scrollOptions_backContainer}>
            <Text style={styles.scrollOptions_screenTitle}>Day Report</Text>
          </TouchableOpacity>

          {/* Action Buttons */}
          <View style={styles.scrollOptions_buttonsContainer}>
            {['New', 'Save', 'View', 'Cancel'].map((option, index) => {
              const isSelected = selectedScrollOption === option;
              let buttonStyle = [styles.scrollOptions_button];

              if (option === 'Cancel') {
                buttonStyle.push({
                  backgroundColor: isSelected ? '#FE0000' : '#FF3333',
                });
              } else if (option === 'Save') {
                buttonStyle.push({
                  backgroundColor: isSelected ? '#02720F' : '#02A515',
                });
              } else {
                buttonStyle.push({
                  backgroundColor: isSelected ? '#02096A' : '#DEDDDD',
                });
              }

              return (
                <View style={styles.scrollOptions_buttonWrapper} key={index}>
                  <TouchableOpacity
                    style={buttonStyle}
                    onPress={() => setSelectedScrollOption(option)}
                  >
                    <Text style={[
                      styles.scrollOptions_buttonText,
                      { color: isSelected ? 'white' : 'black' },
                    ]}>
                      {option}
                    </Text>
                  </TouchableOpacity>
                </View>
              );
            })}
          </View>
        </View>
      </View>

      {/* Main Content ScrollView */}
      <ScrollView style={{ flex: 1 }}>
        <View style={styles.reportContainer}>

          {/* Row 1: Report Type and POS Terminal */}
          <View style={styles.inputRow}>
            <View style={styles.dropdownContainer}>
              <Text style={styles.inputLabel}>Report Type:</Text>
              <Dropdown
                data={[
                  { label: 'Sale - Bill Wise', value: 'sale-bill-wise' },
                  { label: 'Sale - Item Wise', value: 'sale-item-wise' },
                ]}
                labelField="label"
                valueField="value"
                placeholder="Select Report Type"
                value={reportType}
                onChange={(item) => setReportType(item.value)}
                style={styles.dropdown}
              />
            </View>

            <View style={styles.posTerminalContainer}>
              <Text style={styles.inputLabel}>POS Terminal:</Text>
              <View style={styles.posTerminalBox}>
                <TouchableOpacity
                  style={styles.arrowButton}
                  onPress={handlePosTerminalDecrease}
                >
                  <Text style={styles.arrowText}>←</Text>
                </TouchableOpacity>
                <Text style={styles.posTerminalNumber}>{posTerminalNumber}</Text>
                <TouchableOpacity
                  style={styles.arrowButton}
                  onPress={handlePosTerminalIncrease}
                >
                  <Text style={styles.arrowText}>→</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {/* Row 1.5: Business Channel (only show for item-wise reports) */}
          {reportType === 'sale-item-wise' && (
            <View style={styles.inputRow}>
              <View style={styles.dropdownContainer}>
                <Text style={styles.inputLabel}>Business Channel:</Text>
                <Dropdown
                  data={[
            
                    { label: 'POS', value: 'POS' },
                    { label: 'WEB', value: 'WEB' },
                  ]}
                  labelField="label"
                  valueField="value"
                  placeholder="Select Business Channel"
                  value={selectedBusinessChannel}
                  onChange={(item) => setSelectedBusinessChannel(item.value)}
                  style={styles.dropdown}
                />
              </View>
              <View style={styles.dropdownContainer}>
                {/* Empty container for spacing */}
              </View>
            </View>
          )}

          {/* Row 2: Date Pickers */}
          <View style={styles.inputRow}>
            <View style={styles.dateContainer}>
              <Text style={styles.inputLabel}>From Date:</Text>
              <TouchableOpacity
                onPress={() => setShowFromDatePicker(true)}
                style={styles.datePickerButton}
              >
                <Text style={styles.dateText}>
                  {fromDate ? fromDate.toLocaleDateString() : 'Select From Date'}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.dateContainer}>
              <Text style={styles.inputLabel}>To Date:</Text>
              <TouchableOpacity
                onPress={() => setShowToDatePicker(true)}
                style={styles.datePickerButton}
              >
                <Text style={styles.dateText}>
                  {toDate ? toDate.toLocaleDateString() : 'Select To Date'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* From Date Picker */}
          {showFromDatePicker && (
            <DateTimePicker
              value={fromDate || new Date()}
              mode="date"
              display="default"
              onChange={(_, date) => {
                setShowFromDatePicker(false);
                if (date) {
                  setFromDate(date);
                  // Only auto-set toDate if user hasn't picked one yet
                  if (!toDate) {
                    const nextDay = new Date(date);
                    nextDay.setDate(nextDay.getDate() + 1);
                    setToDate(nextDay);
                  }
                }
              }}
            />
          )}

          {/* To Date Picker */}
          {showToDatePicker && (
            <DateTimePicker
              value={toDate || new Date()}
              mode="date"
              display="default"
              onChange={(_, date) => {
                setShowToDatePicker(false);
                if (date) {
                  if (fromDate && date.toDateString() === fromDate.toDateString()) {
                    Alert.alert("Date Error", "From Date and To Date cannot be the same.");
                  } else {
                    setToDate(date);
                  }
                }
              }}
            />
          )}

          {/* Row 3: Generate Report Button */}
          <View style={styles.generateButtonContainer}>
            <TouchableOpacity
              style={styles.generateButton}
              onPress={handleGenerateReport}
            >
              <Text style={styles.generateButtonText}>Generate Report</Text>
            </TouchableOpacity>
          </View>

        </View>
      </ScrollView>
    </View>
  );
};



const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },

  // Scroll Options Styles
  scrollOptions_container: {
    backgroundColor: '#E6E6E6',
    paddingVertical: 8,
    marginTop: 0,
  },
  scrollOptions_row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  scrollOptions_backContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scrollOptions_screenTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: 'black',
  },
  scrollOptions_buttonsContainer: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'flex-end',
  },
  scrollOptions_buttonWrapper: {
    width: '22%',
    marginHorizontal: 5,
  },
  scrollOptions_button: {
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  scrollOptions_buttonText: {
    fontSize: 18,
    fontWeight: 'bold',
  },

  // Report Container Styles
  reportContainer: {
    backgroundColor: '#FFFFFF',
    padding: 15,
    margin: 10,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },

  // Form Styles
  inputRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
    gap: 15,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  dropdownContainer: {
    flex: 1,
  },
  dropdown: {
    height: 50,
    backgroundColor: 'white',
    borderRadius: 10,
    paddingHorizontal: 15,
    borderWidth: 1,
    borderColor: '#02096A',
  },

  // POS Terminal Styles
  posTerminalContainer: {
    flex: 1,
  },
  posTerminalBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#02096A',
    height: 50,
  },
  arrowButton: {
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#02096A',
  },
  arrowText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  posTerminalNumber: {
    flex: 1,
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },

  // Date Picker Styles
  dateContainer: {
    flex: 1,
  },
  datePickerButton: {
    height: 50,
    backgroundColor: 'white',
    borderRadius: 10,
    paddingHorizontal: 15,
    borderWidth: 1,
    borderColor: '#02096A',
    justifyContent: 'center',
  },
  dateText: {
    fontSize: 16,
    color: '#333',
  },

  // Generate Button Styles
  generateButtonContainer: {
    alignItems: 'center',
    marginTop: 30,
  },
  generateButton: {
    backgroundColor: '#02096A',
    paddingHorizontal: 40,
    paddingVertical: 15,
    borderRadius: 10,
    minWidth: 200,
  },
  generateButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },

});

export default DayReportScreen;